import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { FlatButton } from "./flat-button";
import { MenuIcon, X, Plus, Settings } from "lucide-react";

const meta: Meta<typeof FlatButton> = {
  title: "UI/FlatButton",
  component: FlatButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      control: { type: "text" },
      description: "Button content",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    square: {
      control: { type: "boolean" },
      description: "Makes the button square-shaped",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    isLoading: {
      control: { type: "boolean" },
      description: "Shows loading spinner",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    asChild: {
      control: { type: "boolean" },
      description: "Renders as child component using Slot",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables the button",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS classes",
      table: {
        type: { summary: "string" },
      },
    },
    onClick: {
      action: "clicked",
      description: "Click handler",
      table: {
        type: { summary: "function" },
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * The default button with simple text content.
 */
export const Default: Story = {
  args: {
    children: "Button",
  },
  parameters: {
    docs: {
      description: {
        story: "A basic flat button with text content.",
      },
    },
  },
};

/**
 * Button with descriptive text content.
 */
export const WithText: Story = {
  args: {
    children: "Click me",
  },
  parameters: {
    docs: {
      description: {
        story: "Button with more descriptive text content.",
      },
    },
  },
};

/**
 * Square button with icon only - perfect for toolbars and compact layouts.
 */
export const WithIcon: Story = {
  args: {
    children: <MenuIcon />,
    square: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Square variant with icon only. The `square` prop makes the button have equal width and height, perfect for icon-only buttons.",
      },
    },
  },
};

/**
 * Button combining icon and text for clear action indication.
 */
export const WithIconAndText: Story = {
  args: {
    children: (
      <>
        <Plus />
        Add Item
      </>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Button with both icon and text. The icon helps users quickly identify the action.",
      },
    },
  },
};

/**
 * Square button variant for close/cancel actions.
 */
export const Square: Story = {
  args: {
    children: <X />,
    square: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Square button with close icon, commonly used for dismissing modals or removing items.",
      },
    },
  },
};

/**
 * Loading state with spinner animation.
 */
export const Loading: Story = {
  args: {
    children: "Loading...",
    isLoading: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Button in loading state. The text becomes transparent and a spinner is shown.",
      },
    },
  },
};

/**
 * Square button in loading state.
 */
export const LoadingSquare: Story = {
  args: {
    children: <Settings />,
    square: true,
    isLoading: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Square button with loading spinner. The icon is hidden while loading.",
      },
    },
  },
};

/**
 * Disabled button state.
 */
export const Disabled: Story = {
  args: {
    children: "Disabled",
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: "Disabled button that cannot be interacted with.",
      },
    },
  },
};

/**
 * Button with custom styling using className.
 */
export const CustomStyle: Story = {
  args: {
    children: "Custom Style",
    className: "bg-blue-500 text-white hover:bg-blue-600",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Button with custom styling applied via className prop. You can override the default styles.",
      },
    },
  },
};

// All variants showcase
export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-wrap items-center gap-4">
      <FlatButton>Default</FlatButton>
      <FlatButton square>
        <MenuIcon />
      </FlatButton>
      <FlatButton isLoading>Loading</FlatButton>
      <FlatButton disabled>Disabled</FlatButton>
      <FlatButton>
        <Plus />
        With Icon
      </FlatButton>
    </div>
  ),
  parameters: {
    controls: { disable: true },
  },
};
