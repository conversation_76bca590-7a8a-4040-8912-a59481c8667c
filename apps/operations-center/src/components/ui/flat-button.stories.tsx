import type { <PERSON><PERSON>, <PERSON><PERSON>b<PERSON> } from "@storybook/react";
import { FlatButton } from "./flat-button";
import { MenuIcon, X, Plus, Settings, Heart, Star } from "lucide-react";

const meta: Meta<typeof FlatButton> = {
  title: "UI/FlatButton",
  component: FlatButton,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component: `
A versatile flat button component with support for icons, loading states, and square variants.

## Features
- **Flexible content**: Supports text, icons, or both
- **Loading state**: Shows spinner when \`isLoading\` is true
- **Square variant**: Perfect for icon-only buttons
- **Accessible**: Proper focus management and keyboard navigation
- **Customizable**: Accepts custom className for styling overrides

## Usage
\`\`\`tsx
import { FlatButton } from './flat-button';
import { Plus } from 'lucide-react';

// Basic button
<FlatButton>Click me</FlatButton>

// With icon
<FlatButton>
  <Plus />
  Add Item
</FlatButton>

// Square icon button
<FlatButton square>
  <Plus />
</FlatButton>
\`\`\`
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      control: "text",
      description:
        "Button content - can be text, JSX elements, or React components",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "undefined" },
      },
    },
    square: {
      control: "boolean",
      description:
        "Makes the button square-shaped, ideal for icon-only buttons",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    isLoading: {
      control: "boolean",
      description: "Shows loading spinner and makes text transparent",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    asChild: {
      control: "boolean",
      description:
        "Renders as child component using Radix Slot for composition",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    disabled: {
      control: "boolean",
      description: "Disables the button and prevents interactions",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    className: {
      control: "text",
      description: "Additional CSS classes to apply to the button",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "undefined" },
      },
    },
    onClick: {
      action: "clicked",
      description: "Click event handler",
      table: {
        type: { summary: "(event: MouseEvent) => void" },
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * The default button with simple text content.
 */
export const Default: Story = {
  args: {
    children: "Button",
  },
  parameters: {
    docs: {
      description: {
        story: "A basic flat button with text content.",
      },
    },
  },
};

/**
 * Button with descriptive text content.
 */
export const WithText: Story = {
  args: {
    children: "Click me",
  },
  parameters: {
    docs: {
      description: {
        story: "Button with more descriptive text content.",
      },
    },
  },
};

/**
 * Square button with icon only - perfect for toolbars and compact layouts.
 */
export const WithIcon: Story = {
  args: {
    children: <MenuIcon />,
    square: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Square variant with icon only. The `square` prop makes the button have equal width and height, perfect for icon-only buttons.",
      },
    },
  },
};

/**
 * Button combining icon and text for clear action indication.
 */
export const WithIconAndText: Story = {
  args: {
    children: (
      <>
        <Plus />
        Add Item
      </>
    ),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Button with both icon and text. The icon helps users quickly identify the action.",
      },
    },
  },
};

/**
 * Square button variant for close/cancel actions.
 */
export const Square: Story = {
  args: {
    children: <X />,
    square: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Square button with close icon, commonly used for dismissing modals or removing items.",
      },
    },
  },
};

/**
 * Loading state with spinner animation.
 */
export const Loading: Story = {
  args: {
    children: "Loading...",
    isLoading: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Button in loading state. The text becomes transparent and a spinner is shown.",
      },
    },
  },
};

/**
 * Square button in loading state.
 */
export const LoadingSquare: Story = {
  args: {
    children: <Settings />,
    square: true,
    isLoading: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Square button with loading spinner. The icon is hidden while loading.",
      },
    },
  },
};

/**
 * Disabled button state.
 */
export const Disabled: Story = {
  args: {
    children: "Disabled",
    disabled: true,
  },
  parameters: {
    docs: {
      description: {
        story: "Disabled button that cannot be interacted with.",
      },
    },
  },
};

/**
 * Button with custom styling using className.
 */
export const CustomStyle: Story = {
  args: {
    children: "Custom Style",
    className: "bg-blue-500 text-white hover:bg-blue-600",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Button with custom styling applied via className prop. You can override the default styles.",
      },
    },
  },
};

/**
 * Showcase of all button variants in one view.
 */
export const AllVariants: Story = {
  render: () => (
    <div className="space-y-6">
      <div>
        <h3 className="mb-3 text-sm font-medium text-gray-700">Text Buttons</h3>
        <div className="flex flex-wrap items-center gap-3">
          <FlatButton>Default</FlatButton>
          <FlatButton disabled>Disabled</FlatButton>
          <FlatButton isLoading>Loading</FlatButton>
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-sm font-medium text-gray-700">Icon Buttons</h3>
        <div className="flex flex-wrap items-center gap-3">
          <FlatButton square>
            <MenuIcon />
          </FlatButton>
          <FlatButton square>
            <Heart />
          </FlatButton>
          <FlatButton square>
            <Star />
          </FlatButton>
          <FlatButton square disabled>
            <X />
          </FlatButton>
          <FlatButton square isLoading>
            <Settings />
          </FlatButton>
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-sm font-medium text-gray-700">
          Icon + Text Buttons
        </h3>
        <div className="flex flex-wrap items-center gap-3">
          <FlatButton>
            <Plus />
            Add Item
          </FlatButton>
          <FlatButton>
            <Heart />
            Like
          </FlatButton>
          <FlatButton disabled>
            <X />
            Remove
          </FlatButton>
          <FlatButton isLoading>
            <Settings />
            Settings
          </FlatButton>
        </div>
      </div>

      <div>
        <h3 className="mb-3 text-sm font-medium text-gray-700">
          Custom Styled
        </h3>
        <div className="flex flex-wrap items-center gap-3">
          <FlatButton className="bg-blue-500 text-white hover:bg-blue-600">
            Primary
          </FlatButton>
          <FlatButton className="bg-green-500 text-white hover:bg-green-600">
            <Plus />
            Success
          </FlatButton>
          <FlatButton className="bg-red-500 text-white hover:bg-red-600" square>
            <X />
          </FlatButton>
        </div>
      </div>
    </div>
  ),
  parameters: {
    controls: { disable: true },
    docs: {
      description: {
        story:
          "A comprehensive showcase of all FlatButton variants including text buttons, icon buttons, combined icon+text buttons, and custom styled examples.",
      },
    },
  },
};
