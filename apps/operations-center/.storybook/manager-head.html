<style>
/* Fix Storybook UI styling issues */

/* Fix boolean toggle controls */
.docblock-argstable-body .css-1wa3eu0-placeholder {
  display: none;
}

/* Fix "Set boolean" text issue */
.css-1wa3eu0-placeholder::before {
  content: "";
}

/* Fix toggle switch styling */
input[type="checkbox"] {
  appearance: none;
  width: 40px;
  height: 20px;
  background: #ccc;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
  transition: background 0.2s;
}

input[type="checkbox"]:checked {
  background: #007bff;
}

input[type="checkbox"]::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
}

input[type="checkbox"]:checked::before {
  transform: translateX(20px);
}

/* Fix Show code button styling */
.docblock-code-toggle {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.docblock-code-toggle:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* Fix story titles */
.sbdocs-h1, .sbdocs-h2, .sbdocs-h3 {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 16px;
}

.sbdocs-h1 {
  font-size: 32px;
  border-bottom: 1px solid #e1e5e9;
  padding-bottom: 8px;
}

.sbdocs-h2 {
  font-size: 24px;
  margin-top: 32px;
}

.sbdocs-h3 {
  font-size: 18px;
  margin-top: 24px;
}

/* Fix controls panel */
.sidebar-container .sidebar-item {
  padding: 8px 16px;
  border-bottom: 1px solid #e1e5e9;
}

/* Fix table styling */
.docblock-argstable {
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
  overflow: hidden;
}

.docblock-argstable th {
  background: #f8f9fa;
  font-weight: 600;
  padding: 12px 16px;
  border-bottom: 1px solid #e1e5e9;
}

.docblock-argstable td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f5;
  vertical-align: top;
}

.docblock-argstable tbody tr:last-child td {
  border-bottom: none;
}

/* Fix control values display */
.css-1wa3eu0-placeholder {
  color: #6c757d;
  font-style: italic;
}

/* Ensure proper spacing */
.sbdocs-content > * + * {
  margin-top: 16px;
}
</style>
