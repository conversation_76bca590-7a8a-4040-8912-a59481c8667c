import type { Preview } from "@storybook/react";
import "@allo/ui/style.css";
import "../src/globals.css";
import "@fontsource-variable/inter";
import "@fontsource-variable/bricolage-grotesque";

const preview: Preview = {
  parameters: {
    layout: "centered",
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    backgrounds: {
      default: "light",
      values: [
        {
          name: "light",
          value: "#ffffff",
        },
        {
          name: "dark",
          value: "#333333",
        },
      ],
    },
  },
  tags: ["autodocs"],
};

export default preview;
