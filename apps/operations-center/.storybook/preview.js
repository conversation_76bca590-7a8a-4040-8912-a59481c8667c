import "@allo/ui/style.css";
import "../src/globals.css";
import "@fontsource-variable/inter";
import "@fontsource-variable/bricolage-grotesque";
import "./storybook.css";

/** @type { import('@storybook/react').Preview } */
const preview = {
  parameters: {
    layout: "centered",
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
      expanded: true,
      sort: "alpha",
    },
    backgrounds: {
      default: "light",
      values: [
        {
          name: "light",
          value: "#ffffff",
        },
        {
          name: "dark",
          value: "#333333",
        },
      ],
    },
    docs: {
      toc: true,
      source: {
        state: "open",
      },
      canvas: {
        sourceState: "shown",
      },
      controls: {
        sort: "alpha",
        expanded: true,
      },
    },
    options: {
      storySort: {
        order: ["UI", "*"],
      },
    },
  },
  tags: ["autodocs"],
  globalTypes: {
    theme: {
      description: "Global theme for components",
      defaultValue: "light",
      toolbar: {
        title: "Theme",
        icon: "circlehollow",
        items: ["light", "dark"],
        dynamicTitle: true,
      },
    },
  },
};

export default preview;
