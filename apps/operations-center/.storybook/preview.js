import "@allo/ui/style.css";
import "../src/globals.css";
import "@fontsource-variable/inter";
import "@fontsource-variable/bricolage-grotesque";
import "./storybook-fixes.css";

/** @type { import('@storybook/react').Preview } */
const preview = {
  parameters: {
    layout: "centered",
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
      hideNoControlsWarning: true,
    },
    backgrounds: {
      default: "light",
      values: [
        {
          name: "light",
          value: "#ffffff",
        },
        {
          name: "dark",
          value: "#333333",
        },
      ],
    },
    docs: {
      source: {
        state: "open",
      },
    },
  },
  tags: ["autodocs"],
};

export default preview;
