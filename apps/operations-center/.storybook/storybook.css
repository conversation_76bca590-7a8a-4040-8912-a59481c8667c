/* Custom Storybook styling fixes */

/* Fix documentation table styling */
.docblock-argstable {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin: 1rem 0;
}

.docblock-argstable th,
.docblock-argstable td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  text-align: left;
  vertical-align: top;
}

.docblock-argstable th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #d1d5db;
}

.docblock-argstable tbody tr:hover {
  background-color: #f9fafb;
}

/* Fix controls panel styling */
.sidebar-container {
  background: #ffffff;
}

.sidebar-item {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

/* Improve code block styling */
.docblock-code-toggle {
  margin: 1rem 0;
}

.prismjs {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 1rem;
}

/* Fix story preview styling */
.docs-story {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 2rem;
  margin: 1rem 0;
}

/* Improve typography in docs */
.sbdocs-content h1,
.sbdocs-content h2,
.sbdocs-content h3 {
  color: #111827;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.sbdocs-content p {
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* Fix button component specific styling */
.flat-button-showcase {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

/* Improve controls panel */
.controls-panel {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
}

/* Fix table responsive issues */
@media (max-width: 768px) {
  .docblock-argstable {
    font-size: 0.875rem;
  }
  
  .docblock-argstable th,
  .docblock-argstable td {
    padding: 0.5rem;
  }
}
