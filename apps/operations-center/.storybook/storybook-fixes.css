/* Storybook UI fixes for FlatButton documentation */

/* Fix boolean controls - remove "FalseTrue" text issue */
.css-1wa3eu0-placeholder,
.css-1wa3eu0-placeholder::before,
.css-1wa3eu0-placeholder::after {
  display: none !important;
  content: none !important;
}

/* Fix boolean toggle switches */
.css-1wa3eu0-control input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  width: 40px;
  height: 20px;
  background: #e2e8f0;
  border-radius: 10px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  outline: none;
}

.css-1wa3eu0-control input[type="checkbox"]:checked {
  background: #3b82f6;
}

.css-1wa3eu0-control input[type="checkbox"]::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: white;
  top: 2px;
  left: 2px;
  transition: transform 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.css-1wa3eu0-control input[type="checkbox"]:checked::before {
  transform: translateX(20px);
}

/* Fix "Set boolean" text */
.css-1wa3eu0-control .css-1wa3eu0-placeholder {
  display: none !important;
}

/* Fix Show code button */
.docblock-code-toggle,
button[title="Show code"] {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #475569;
  cursor: pointer;
  transition: all 0.15s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.docblock-code-toggle:hover,
button[title="Show code"]:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #334155;
}

/* Fix story section titles */
.sbdocs-h1,
.sbdocs-h2,
.sbdocs-h3,
.sbdocs-h4 {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-weight: 600;
  line-height: 1.25;
  color: #1e293b;
  margin: 0;
}

.sbdocs-h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.sbdocs-h2 {
  font-size: 1.5rem;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.sbdocs-h3 {
  font-size: 1.25rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

/* Fix controls table */
.docblock-argstable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  margin: 1rem 0;
}

.docblock-argstable th {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.docblock-argstable td {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f5f9;
  font-size: 14px;
  vertical-align: top;
  color: #475569;
}

.docblock-argstable tbody tr:last-child td {
  border-bottom: none;
}

.docblock-argstable tbody tr:hover {
  background: #f8fafc;
}

/* Fix control inputs */
.css-1wa3eu0-control input[type="text"] {
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  width: 100%;
  max-width: 200px;
}

.css-1wa3eu0-control input[type="text"]:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Fix story descriptions */
.sbdocs-p {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* Fix code blocks */
.prismjs {
  background: #f8fafc !important;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  font-size: 14px;
  line-height: 1.5;
}
